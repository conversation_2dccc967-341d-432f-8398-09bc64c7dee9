<script setup lang="ts">
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { useRoute } from 'vue-router'

  const { actions:{refetchSingleParticipant} } = useParticipants()

  const route = useRoute()
  const id = computed(() => route.params.id as string)

  watch(
    () => id.value,
    () => {
      refetchSingleParticipant()
    },
    { immediate: true }
  )
</script>

<template>
  <NewBasicInformation :editor-view="true" />
  <NewParticipantInformation :show-reject-options="false" :editor-view="true" />
  <NewEmploymentInfo :show-reject-options="false" :editor-view="true" />
  <NewPensionInfo :show-reject-options="false" :editor-view="true" />
  <NewSalaryPensionBase :show-reject-options="false" :editor-view="true" />
</template>

<style scoped lang="scss">
</style>