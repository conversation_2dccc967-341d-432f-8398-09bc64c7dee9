<script setup lang="ts">
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { useRoute } from 'vue-router'

  const { actions:{refetchSingleParticipant} } = useParticipants()

  const route = useRoute()
  const id = computed(() => route.params.id as string)

  watch(
    () => id.value,
    () => {
      refetchSingleParticipant()
    },
    { immediate: true }
  )
</script>

<template>
  <NewBasicInformation />
  <NewParticipantInformation :show-reject-options="true" />
  <NewEmploymentInfo :show-reject-options="true" />
  <NewPensionInfo :show-reject-options="true" />
  <NewSalaryPensionBase :show-reject-options="true" />
</template>

<style scoped lang="scss">
</style>