<script setup lang="ts">
  import { onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { useParticipants } from '@/composables/participants/useParticipants'

  const {
    state: { participantsList, loadingParticipants },
    actions: { refetchParticipants }
  } = useParticipants()

  const router = useRouter()

  const searchInput = ref('');
  const selectedStatus = ref('All Status');
  const sortBy = ref('firstName'
  );
  const sortDirection = ref('asc');
  const isLoading = ref(false);
  const appliedFilters = ref({});
  const page = ref(1);
  const itemsPerPage = ref(5);
  const statusOptions = ref(['All Status', 'Active', 'Pending', 'Inactive']);

  const headers = ref([
    { title: 'LAST NAME', key: 'lastName', sortable: true },
    { title: 'FIRST NAME', key: 'firstName', sortable: true },
    { title: 'BIRTH DATE', key: 'birthDate', sortable: true },
    { title: 'STATUS', key: 'status', sortable: true },
    { title: 'APPROVAL STATUS', key: 'approvalStatus', sortable: true },
    { title: '', key: 'actions', sortable: false}
  ]);

  const filteredParticipants = computed(() => {
    let filtered = [...participantsList.value];

    if (searchInput.value.trim()) {
      const search = searchInput.value.toLowerCase();
      filtered = filtered.filter(p =>
        p.lastName.toLowerCase().includes(search) ||
        p.firstName.toLowerCase().includes(search)
      );
    }

    if (selectedStatus.value !== 'All Status') {
      filtered = filtered.filter(p => p.status === selectedStatus.value);
    }

    if (appliedFilters.value) {
      const filters = appliedFilters.value as any;

      if (filters.status) {
        filtered = filtered.filter(p => p.status.toLowerCase() === filters.status.toLowerCase());
      }

      if (filters.birthDateRange && filters.birthDateRange.startDate && filters.birthDateRange.endDate) {
        const startDate = new Date(filters.birthDateRange.startDate);
        const endDate = new Date(filters.birthDateRange.endDate);

        filtered = filtered.filter(p => {
          const birthDate = new Date(p.birthDate);
          return birthDate >= startDate && birthDate <= endDate;
        });
      }
    }

    return filtered;
  });

  const paginationMeta = computed(() => {
    const start = filteredParticipants.value.length ? (page.value - 1) * itemsPerPage.value + 1 : 0;
    const end = Math.min(start + itemsPerPage.value - 1, filteredParticipants.value.length);
    return `Showing ${start} to ${end} of ${filteredParticipants.value.length} entries`;
  });

  const handleSort = (newSortOptions: any) => {
    sortBy.value = newSortOptions[0].key;
    sortDirection.value = newSortOptions[0].order;
  };

  const resetSearch = () => {
    searchInput.value = '';
    selectedStatus.value = 'All Status';
    appliedFilters.value = {};
    page.value = 1;
  };

  const viewParticipant = (participant: any) => {
    if (participant.approvalStatus === 'PENDING') {
      router.push(`change-proposals/new-participants/review/${participant.id}`);
    } else {
      router.push(`/participants/${participant.id}`);
    }
  };

  const addParticipant = () => {
    router.push('/participants/add')
  };

  // Refresh participants list when component is mounted
  onMounted(async () => {
    try {
      await refetchParticipants()
    } catch (error) {
      console.error('Error refreshing participants:', error)
    }
  })

</script>

<template>
  <div class="participants-container">
    <div class="header-container">
      <div>
        <h1 class="text-h4 font-weight-bold">Participants</h1>
        <p class="text-subtitle-1 text-grey-darken-1">Manage pension participants and their information</p>
      </div>
      <v-btn
        color="primary"
        prepend-icon="tabler-plus"
        variant="flat"
        @click="addParticipant"
      >
        Add Participant
      </v-btn>
    </div>


    <!-- Search and filter bar -->
    <v-card variant="flat" class="mb-8 pa-4 mt-8">
      <div class="d-flex flex-wrap gap-4">
        <div class="search-container flex-grow-1">
          <p class="mb-2 font-weight-medium">Search Participants</p>
          <v-text-field
            v-model="searchInput"
            placeholder="Search by name"
            density="comfortable"
            variant="outlined"
            bg-color="white"
            hide-details
            append-inner-icon="mdi-magnify"
            class="search-field"
          ></v-text-field>
        </div>

        <div class="status-filter">
          <p class="mb-2 font-weight-medium">Status Filter</p>
          <v-select
            v-model="selectedStatus"
            :items="statusOptions"
            variant="outlined"
            density="comfortable"
            hide-details
            class="status-select"
          ></v-select>
        </div>
      </div>
    </v-card>

    <!-- Participants table using v-data-table -->
    <v-skeleton-loader
      v-if="loadingParticipants"
      class="mx-auto border py-6"
      type="table"
    ></v-skeleton-loader>
    <v-card variant="outlined" class="participants-table" v-else>
      <v-data-table
        v-model:page="page"
        v-model:items-per-page="itemsPerPage"
        :headers="headers"
        :items="filteredParticipants"
        :loading="isLoading"
        :sort-by="[{ key: sortBy}]"
        class="elevation-0"
        @update:sort-by="handleSort"
        :items-per-page-options="[5, 10, 25, 50]"
      >
        <!-- Row styling for PENDING participants -->
        <template v-slot:item="{ item, props: itemProps }">
          <tr 
            v-bind="itemProps"
            :class="{ 'pending-participant-row': item.approvalStatus === 'PENDING' }"
          >
            <td :class="{ 'dimmed-text': item.approvalStatus === 'PENDING' }">{{ item.lastName }}</td>
            <td :class="{ 'dimmed-text': item.approvalStatus === 'PENDING' }">{{ item.firstName }}</td>
            <td :class="{ 'dimmed-text': item.approvalStatus === 'PENDING' }">{{ item.birthDate }}</td>
            <td>
              <v-chip
                size="small"
                :color="item.status === 'Active' ? 'success' : 'error'"
                text-color="white"
                class="font-weight-medium"
                :class="{ 'dimmed-chip': item.approvalStatus === 'PENDING' }"
              >
                {{ item.status }}
              </v-chip>
            </td>
            <td>
              <v-chip
                size="small"
                :color="item.approvalStatus === 'PENDING' ? 'warning' : 'success'"
                text-color="white"
                class="font-weight-medium"
              >
                {{ item.approvalStatus || 'APPROVED' }}
              </v-chip>
            </td>
            <td>
              <v-btn
                :color="item.approvalStatus === 'PENDING' ? 'warning' : 'primary'"
                variant="text"
                @click="viewParticipant(item)"
              >
                {{ item.approvalStatus === 'PENDING' ? 'Review' : 'View' }}
              </v-btn>
            </td>
          </tr>
        </template>

        <!-- Empty state -->
        <template v-slot:no-data>
          <div class="d-flex flex-column justify-center align-center pa-4">
            <span class="text-subtitle-1 mb-2">No participants found</span>
            <v-btn
              v-if="searchInput || selectedStatus !== 'All Status' || Object.keys(appliedFilters).length > 0"
              variant="outlined"
              size="small"
              color="primary"
              @click="resetSearch"
            >
              Clear Filters
            </v-btn>
          </div>
        </template>

        <!-- Pagination -->
        <template #bottom>
          <v-divider />
          <div class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3">
            <p class="text-sm text-disabled mb-0">
              {{ paginationMeta }}
            </p>

            <v-pagination
              v-model="page"
              :length="Math.ceil(filteredParticipants.length / itemsPerPage)"
              :total-visible="$vuetify ? ($vuetify.display.xs ? 1 : $vuetify.display.md ? 5 : 7) : 7"
            >
              <template #prev="slotProps">
                <v-btn
                  variant="tonal"
                  color="default"
                  v-bind="slotProps"
                  :icon="false"
                >
                  Previous
                </v-btn>
              </template>

              <template #next="slotProps">
                <v-btn
                  variant="tonal"
                  color="default"
                  v-bind="slotProps"
                  :icon="false"
                >
                  Next
                </v-btn>
              </template>
            </v-pagination>

            <v-select
              v-model="itemsPerPage"
              :items="[5, 10, 25, 50]"
              label="Per page"
              density="compact"
              variant="outlined"
              hide-details
              class="items-per-page-select"
            ></v-select>
          </div>
        </template>
      </v-data-table>
    </v-card>

  </div>
</template>

<style scoped>
  .participants-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
  }

  .search-container {
    min-width: 280px;
  }

  .status-filter {
    min-width: 200px;
  }

  .search-button {
    height: 56px;
  }

  .participants-table {
    border-radius: 8px;
    overflow: hidden;
  }

  .items-per-page-select {
    min-width: 80px;
  }

  :deep(.v-data-table) {
    border-radius: 8px;
  }

  :deep(.v-data-table__th) {
    font-weight: 600;
    white-space: nowrap;
  }

  @media (max-width: 600px) {
    .header-container {
      flex-direction: column;
      gap: 16px;
    }

    .search-container,
    .status-filter {
      width: 100%;
    }
  }

  /* Styles for PENDING participants */
  .pending-participant-row {
    background-color: rgba(255, 193, 7, 0.05) !important;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .dimmed-chip {
    opacity: 0.7;
  }

  .pending-participant-row:hover {
    background-color: rgba(255, 193, 7, 0.1) !important;
  }
</style>