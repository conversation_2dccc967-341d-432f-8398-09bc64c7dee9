<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { useAppStore } from '@/stores/app/appStore'
  import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'

  const props = defineProps({
    showRejectOptions: {
      type: Boolean,
      default: true
    },
    editorView: {
      type: Boolean,
      default: false
    }
  });

  const { state: { participantExPartnerInfo, participantDetails, loadingParticipant }, actions: { refetchSingleParticipant } } = useParticipants()
  const appStore = useAppStore()

  const emit = defineEmits(['field-rejected']);

  const editDialog = ref(false);
  const selectedField = ref({
    field: '',
    value: '',
    id: '',
    partnerIndex: 0,
    typename: ''
  });

  const rejectDialog = ref(false);
  const fieldToReject = ref(null);

  // Get rejected fields with reasons for editor view
  const rejectedFieldsWithReasons = computed(() => {
    if (!props.editorView) return [];

    const rejectedFieldsArray: Array<{ field: string, fieldKey: string, reason: string, partnerIndex: number, partnerName: string }> = [];

    // Check each ex-partner for rejection reasons
    participantExPartnerInfo.value.forEach((partner: any[], partnerIndex: number) => {
      const partnerName = getPartnerName(partner);
      
      // Find certification reject reasons for this partner
      partner.forEach((item: any) => {
        if (item.field === 'certificationRejectReason' && Array.isArray(item.value)) {
          item.value.forEach((rejectItem: any) => {
            if (rejectItem.reason && rejectItem.status === 'VALID' && isPartnerField(rejectItem.field)) {
              rejectedFieldsArray.push({
                field: fieldKeyToName(rejectItem.field),
                fieldKey: rejectItem.field,
                reason: rejectItem.reason,
                partnerIndex,
                partnerName
              });
            }
          });
        }
      });
    });

    return rejectedFieldsArray;
  });

  // Get rejected fields based on pendingChanges array (for reviewer view)
  const rejectedFields = computed(() => {
    if (!props.showRejectOptions || props.editorView) return [];

    const rejectedFieldsArray: Array<{ field: string, fieldKey: string, partnerIndex: number, partnerName: string }> = [];

    // Check each ex-partner for pending changes
    participantExPartnerInfo.value.forEach((partner: any[], partnerIndex: number) => {
      const partnerName = getPartnerName(partner);
      
      partner.forEach((item: any) => {
        if (item.field === 'pendingChanges' && Array.isArray(item.value)) {
          item.value.forEach((fieldKey: string) => {
            if (isPartnerField(fieldKey)) {
              rejectedFieldsArray.push({
                field: fieldKeyToName(fieldKey),
                fieldKey: fieldKey,
                partnerIndex,
                partnerName
              });
            }
          });
        }
      });
    });

    return rejectedFieldsArray;
  });

  // Check if a field is partner-related
  function isPartnerField(fieldKey: string): boolean {
    const partnerFields = ['firstName', 'lastName', 'dateOfBirth', 'startDate', 'isDeceased'];
    return partnerFields.includes(fieldKey);
  }

  function fieldKeyToName(fieldKey: string): string {
    if (!fieldKey) return '';
    const result = fieldKey.replace(/([A-Z])/g, ' $1');
    return result.charAt(0).toUpperCase() + result.slice(1).trim();
  }

  function getPartnerName(partner: any[]): string {
    const firstNameField = partner.find(item => item.field === 'firstName');
    const lastNameField = partner.find(item => item.field === 'lastName');
    const firstName = firstNameField?.value || '';
    const lastName = lastNameField?.value || '';
    return `${firstName} ${lastName}`.trim() || 'Ex-Partner';
  }

  const filteredPartners = computed(() => {
    return participantExPartnerInfo.value.map(partner =>
      partner.filter(item => item.field !== 'id' && item.field !== 'isCurrent' && item.field !== 'pendingChanges' && item.field !== 'certificationRejectReason')
    );
  });

  const refreshing = ref(false);

  const openEditDialog = (item: any, partnerIndex: number) => {
    if (item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return;
    }

    selectedField.value = {
      field: item.field,
      value: item.value,
      id: item.id,
      partnerIndex,
      typename: item.typename
    };
    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const updateField = (newValue: any) => {
    closeEditDialog();
  };

  const openRejectDialog = (item: any, partnerIndex: number) => {
    if (!props.showRejectOptions || refreshing.value || props.editorView) {
      return;
    }

    // Add partnerIndex to the field item for context
    fieldToReject.value = { ...item, partnerIndex };
    rejectDialog.value = true;
  };

  const closeRejectDialog = () => {
    rejectDialog.value = false;
    fieldToReject.value = null;
  };

  const handleFieldRejected = async (rejectionData: any) => {
    closeRejectDialog();
    emit('field-rejected', rejectionData);

    // Set refreshing state and show loading feedback
    refreshing.value = true;
    appStore.showSnack('Updating field status...');

    // Refresh participant data to get updated values with rejection information
    try {
      await refetchSingleParticipant();
      appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`);
    } catch (error) {
      console.error('Error refreshing participant data:', error);
      appStore.showSnack('Error updating field status');
    } finally {
      refreshing.value = false;
    }
  };

  const getFieldValue = (fieldName: string) => {
    const field = participantDetails.value.find((item: any) => item.field === fieldName);
    return field ? field.value : '';
  };

  const fullName = computed(() => {
    const firstName = getFieldValue('firstName');
    const lastName = getFieldValue('lastName');
    return `${firstName} ${lastName}`;
  });
</script>

<template>
  <div class="ex-partners-container">
    <div class="header-container">
      <h2>Ex-Partners</h2>
    </div>

    <!-- Alerts for rejected fields with reasons (Editor View) -->
    <div v-if="rejectedFieldsWithReasons.length > 0 && editorView" class="mb-4">
      <VAlert
        v-for="(item, index) in rejectedFieldsWithReasons"
        :key="`rejected-reason-${item.fieldKey}-${item.partnerIndex}-${index}`"
        type="error"
        variant="tonal"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            icon="tabler-x"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.partnerName }} - {{ item.field }}:</strong>
          <span>{{ item.reason }}</span>
          <VChip
            size="small"
            color="error"
            class="ml-auto"
          >
            Needs Fix
          </VChip>
        </div>
      </VAlert>
    </div>

    <!-- Simple alerts for rejected fields (Reviewer View) -->
    <div v-if="rejectedFields.length > 0 && !editorView" class="mb-4">
      <VAlert
        v-for="(item, index) in rejectedFields"
        :key="`rejected-${item.fieldKey}-${item.partnerIndex}-${index}`"
        type="info"
        variant="tonal"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            icon="tabler-info-circle"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.partnerName }} - {{ item.field }}:</strong>
          <span>Field has been rejected and is pending review</span>
          <VChip
            size="small"
            color="primary"
            class="ml-auto"
          >
            Follow-up submitted
          </VChip>
        </div>
      </VAlert>
    </div>

    <div v-if="filteredPartners.length > 0">
      <div
        v-for="(partner, partnerIndex) in filteredPartners"
        :key="partner[0]?.id || `partner-${partnerIndex}`"
        class="partner-section"
      >
        <div class="partner-header">
          <h3>{{ getPartnerName(participantExPartnerInfo[partnerIndex]) }}</h3>
        </div>

        <VDataTable
          :headers="[
            { title: 'Field', key: 'name', width: '200px' },
            { title: 'Value', key: 'value' },
            { title: 'Actions', key: 'actions', sortable: false },
          ]"
          :items="partner"
          hide-default-footer
          class="elevation-0"
          density="compact"
        >
          <!-- Custom row styling for disabled fields -->
          <template #item.name="{ item }">
            <span :class="{ 'dimmed-text': item.disabled }">{{ item.name }}</span>
          </template>

          <template #item.value="{ item }">
            <template v-if="item.disabled">
              <VChip
                size="small"
                color="error"
                variant="tonal"
                class="font-weight-medium"
              >
                {{ item.value }}
                <v-tooltip activator="parent" location="top">
                  This field was rejected and is pending review
                </v-tooltip>
              </VChip>
            </template>
            <template v-else>
              <span :class="{ 'dimmed-text': item.disabled }">{{ item.value }}</span>
            </template>
          </template>

          <template #item.actions="{ item }">
            <div class="d-flex gap-1">
              <!-- Show warning icon for rejected/disabled fields -->
              <template v-if="item.disabled">
                <VIcon
                  size="16"
                  icon="tabler-alert-triangle"
                  color="error"
                >
                  <v-tooltip activator="parent" location="top">
                    This field was rejected and is pending review
                  </v-tooltip>
                </VIcon>
              </template>

              <!-- Reject Button (only show for reviewer view and non-disabled fields) -->
              <template v-else-if="showRejectOptions && !editorView && !item.disabled && !refreshing">
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  color="error"
                  @click="openRejectDialog(item, partnerIndex)"
                >
                  <VIcon
                    size="16"
                    icon="tabler-x"
                    class="reject-icon"
                  />
                  <v-tooltip activator="parent" location="top">
                    Reject this field
                  </v-tooltip>
                </VBtn>
              </template>

              <!-- Show loading spinner while refreshing (reviewer view only) -->
              <template v-else-if="showRejectOptions && !editorView && !item.disabled && refreshing">
                <VBtn
                  icon
                  size="small"
                  variant="text"
                  disabled
                >
                  <VIcon
                    size="16"
                    icon="tabler-loader-2"
                    class="spinning"
                    color="primary"
                  />
                  <v-tooltip activator="parent" location="top">
                    Updating field status...
                  </v-tooltip>
                </VBtn>
              </template>
            </div>
          </template>
        </VDataTable>
      </div>
    </div>

    <div v-else class="no-partners-message">
      No ex-partners found
    </div>

    <!-- Dynamic Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="fullName"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />
  </div>
</template>

<style scoped>
  .ex-partners-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .partner-section {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .partner-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
  }

  .no-partners-message {
    padding: 16px;
    text-align: center;
    color: #666;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>