<script setup lang="ts">
  import { useQuery } from '@vue/apollo-composable'
  import { GET_PARTICIPANT_BY_ID } from '@/api/graphql/queries/participantQueries'
  import { ChangeType, SalaryEntry } from '@/gql/graphql'
  import { useAppStore } from '@/stores/app/appStore'
  import { usePensionStore } from '@/stores/pension/pensionStore'
  import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'

  const props = defineProps({
    participantId: {
      type: String,
      required: true
    },
    employmentInfoId: {
      type: String,
      required: true
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    showRejectOptions: {
      type: Boolean,
      default: true
    },
    editorView: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits(['refresh', 'field-rejected'])

  const entries = ref<SalaryEntry[]>([])
  const loading = ref(true)
  const dialogVisible = ref(false)
  const participantName = ref('')
  const selectedEntry = ref<SalaryEntry | null>(null)
  const currentYear = new Date().getFullYear()
  const appStore = useAppStore()
  const pensionStore = usePensionStore()

  const rejectDialog = ref(false);
  const fieldToReject = ref(null as any);

  const certifiedYears = computed(() => {
    return pensionStore.certifiedDataYears
  })

  const { result, loading: queryLoading, refetch } = useQuery(
    GET_PARTICIPANT_BY_ID,
    { id: props.participantId },
    { fetchPolicy: 'network-only' }
  )

  // Get rejected fields with reasons for editor view
  const rejectedFieldsWithReasons = computed(() => {
    if (!props.editorView) return [];

    const rejectedFieldsArray: Array<{ field: string, fieldKey: string, reason: string, entryYear: number }> = [];

    // Check each salary entry's certification reject reasons
    entries.value.forEach((entry: any) => {
      if (entry.certificationRejectReason && Array.isArray(entry.certificationRejectReason)) {
        entry.certificationRejectReason.forEach((item: any) => {
          if (item.reason && item.status === 'VALID' && isSalaryField(item.field)) {
            rejectedFieldsArray.push({
              field: fieldKeyToName(item.field),
              fieldKey: item.field,
              reason: item.reason,
              entryYear: entry.year
            });
          }
        });
      }
    });

    return rejectedFieldsArray;
  });

  // Check if a field is salary-related
  function isSalaryField(fieldKey: string): boolean {
    const salaryFields = ['amount'];
    return salaryFields.includes(fieldKey);
  }

  function fieldKeyToName(fieldKey: string): string {
    if (!fieldKey) return '';
    const fieldNames: Record<string, string> = {
      'amount': 'Salary Amount'
    };
    return fieldNames[fieldKey] || fieldKey.replace(/([A-Z])/g, ' $1').charAt(0).toUpperCase() + fieldKey.slice(1);
  }

  // Format currency values
  const formatCurrency = (value: number): string => {
    return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  // Check if entry is from current year
  const isCurrentYear = (year: number): boolean => {
    return year === currentYear
  }

  // Helper function to determine status color
  const getStatusColor = (year: number): string => {
    if (isCurrentYear(year)) return 'primary'
    return 'success'
  }

  const isDisabled = (pendingChanges: string[] | undefined | null) => {
    return pendingChanges?.includes('amount') || false
  }

  const isCertifiedYear = (year: number) => {
    return certifiedYears.value.includes(year)
  }

  const refreshing = ref(false);

  const openRejectDialog = (entry: any) => {
    if (!props.showRejectOptions || refreshing.value || props.editorView) {
      return;
    }

    fieldToReject.value = {
      field: 'amount',
      name: 'Salary Amount',
      value: formatCurrency(entry.amount),
      id: entry.id,
      typename: 'SalaryEntry',
      year: entry.year
    };
    rejectDialog.value = true;
  };

  const closeRejectDialog = () => {
    rejectDialog.value = false;
    fieldToReject.value = null;
  };

  const handleFieldRejected = async (rejectionData: any) => {
    closeRejectDialog();
    emit('field-rejected', rejectionData);

    // Set refreshing state and show loading feedback
    refreshing.value = true;
    appStore.showSnack('Updating field status...');

    // Refresh participant data to get updated values with rejection information
    try {
      await fetchEntries();
      appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`);
    } catch (error) {
      console.error('Error refreshing participant data:', error);
      appStore.showSnack('Error updating field status');
    } finally {
      refreshing.value = false;
    }
  };

  const fetchEntries = async () => {
    loading.value = true
    try {
      await refetch()
      if (result.value?.getParticipantById?.employmentInfo?.salaryEntries) {
        const sortedEntries = [...result.value.getParticipantById.employmentInfo.salaryEntries].sort((a: any, b: any) => b.year - a.year)
        entries.value = sortedEntries
        participantName.value = result.value.getParticipantById.personalInfo?.firstName + ' ' + result.value.getParticipantById.personalInfo?.lastName
      } else {
        entries.value = []
      }
      emit('refresh')
    } catch (error) {
      console.error('Error fetching salary entries:', error)
      entries.value = []
    } finally {
      loading.value = false
    }
  }

  const openEditDialog = (entry: SalaryEntry) => {
    if(isDisabled(entry.pendingChanges)) {
      appStore.showSnack('Sorry you cannot edit this field.')
      return
    }
    selectedEntry.value = entry
    dialogVisible.value = true
  }

  onMounted(fetchEntries)
</script>

<template>
  <v-card variant="outlined" class="mb-4">
    <v-card-title class="py-3">
      <h4 class="text-subtitle-1 font-weight-medium">Gross Part-time Monthly Salary</h4>
    </v-card-title>
    <v-card-subtitle class="text-caption text-medium-emphasis">
      Amount as of January 1st or start date if started during the year
    </v-card-subtitle>

    <!-- Alerts for rejected fields with reasons (Editor View) -->
    <div v-if="rejectedFieldsWithReasons.length > 0 && editorView" class="mx-4 mt-4">
      <VAlert
        v-for="(item, index) in rejectedFieldsWithReasons"
        :key="`rejected-reason-${item.fieldKey}-${item.entryYear}-${index}`"
        type="error"
        variant="tonal"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            icon="tabler-x"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.field }} ({{ item.entryYear }}):</strong>
          <span>{{ item.reason }}</span>
          <VChip
            size="small"
            color="error"
            class="ml-auto"
          >
            Needs Fix
          </VChip>
        </div>
      </VAlert>
    </div>

    <v-card-text>
      <v-table class="salary-table" density="comfortable">
        <thead>
          <tr>
            <th class="text-left">YEAR</th>
            <th class="text-left">GROSS PART-TIME MONTHLY SALARY</th>
            <th class="text-center">STATUS</th>
            <th v-if="canEdit || showRejectOptions" class="text-center">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td :colspan="canEdit || showRejectOptions ? 4 : 3" class="text-center">
              <v-progress-circular indeterminate color="primary" />
            </td>
          </tr>
          <tr v-else-if="entries.length === 0">
            <td :colspan="canEdit || showRejectOptions ? 4 : 3" class="text-center">No salary entries found</td>
          </tr>
          <template v-else>
            <tr 
              v-for="entry in entries" 
              :key="entry.id" 
              :class="[
                isCurrentYear(entry.year) ? 'bg-blue-lighten-5' : '',
                isDisabled(entry.pendingChanges) ? 'dimmed-row' : ''
              ]"
            >
              <td class="font-weight-medium">{{ entry.year }}</td>
              <td>
                <template v-if="isDisabled(entry.pendingChanges)">
                  <VChip
                    size="small"
                    color="error"
                    variant="tonal"
                    class="font-weight-medium"
                  >
                    Afl. {{ formatCurrency(entry.amount) }}
                    <v-tooltip activator="parent" location="top">
                      This field was rejected and is pending review
                    </v-tooltip>
                  </VChip>
                </template>
                <template v-else>
                  <span :class="{ 'dimmed-text': isDisabled(entry.pendingChanges) }">
                    Afl. {{ formatCurrency(entry.amount) }}
                  </span>
                </template>
              </td>
              <td class="text-center">
                <v-chip :color="getStatusColor(entry.year)" size="small" label>
                  {{ isCurrentYear(entry.year) ? 'Current' : 'Certified' }}
                </v-chip>
              </td>
              <td v-if="canEdit || showRejectOptions" class="text-center">
                <div class="d-flex gap-1 justify-center">
                  <!-- Show warning icon for rejected/disabled fields -->
                  <template v-if="isDisabled(entry.pendingChanges)">
                    <VIcon
                      size="16"
                      icon="tabler-alert-triangle"
                      color="error"
                    >
                      <v-tooltip activator="parent" location="top">
                        This field was rejected and is pending review
                      </v-tooltip>
                    </VIcon>
                  </template>

                  <!-- Reject Button (only show for reviewer view and non-disabled fields) -->
                  <template v-else-if="showRejectOptions && !editorView && !isDisabled(entry.pendingChanges) && !refreshing">
                    <VBtn
                      icon
                      size="small"
                      variant="text"
                      color="error"
                      @click="openRejectDialog(entry)"
                    >
                      <VIcon
                        size="16"
                        icon="tabler-x"
                        class="reject-icon"
                      />
                      <v-tooltip activator="parent" location="top">
                        Reject this field
                      </v-tooltip>
                    </VBtn>
                  </template>

                  <!-- Edit Button (only show if canEdit and not certified year) -->
                  <template v-if="canEdit && !isCertifiedYear(entry.year)">
                    <VBtn
                      icon
                      size="small"
                      variant="text"
                      color="primary"
                      @click="openEditDialog(entry)"
                    >
                      <VIcon
                        size="16"
                        icon="tabler-edit"
                        class="edit-icon"
                        color="primary"
                      />
                      <v-tooltip activator="parent" location="top">
                        Edit this entry
                      </v-tooltip>
                    </VBtn>
                  </template>

                  <!-- Show loading spinner while refreshing (reviewer view only) -->
                  <template v-else-if="showRejectOptions && !editorView && !isDisabled(entry.pendingChanges) && refreshing">
                    <VBtn
                      icon
                      size="small"
                      variant="text"
                      disabled
                    >
                      <VIcon
                        size="16"
                        icon="tabler-loader-2"
                        class="spinning"
                        color="primary"
                      />
                      <v-tooltip activator="parent" location="top">
                        Updating field status...
                      </v-tooltip>
                    </VBtn>
                  </template>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
      </v-table>
    </v-card-text>

    <SalaryEntityDialog
      :editMode="true"
      v-model="dialogVisible"
      :path="`amount`"
      :entityId="selectedEntry?.id || ''"
      :entityType="`SalaryEntry`"
      :entry="selectedEntry as any"
      formType="salary"
      :participantName="participantName"
      :type="ChangeType.Participant"
      :year="selectedEntry?.year"
      @refresh="fetchEntries"
    />

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="participantName"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />
  </v-card>
</template>

<style scoped>
  .salary-table th {
    font-weight: 500;
    background-color: #f5f5f5;
  }

  .bg-blue-lighten-5 {
    background-color: rgba(66, 165, 245, 0.1);
  }

  .dimmed-row {
    opacity: 0.8;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .edit-icon {
    margin-left: 4px;
  }
</style> 