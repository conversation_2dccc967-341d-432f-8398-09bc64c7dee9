import { useMutation } from '@vue/apollo-composable'
import { REJECT_FIELD } from '@/api/graphql/mutations/genericRejectionMutations'
import { useAppStore } from '@/stores/app/appStore'

export interface RejectFieldInput {
    entityId: string
    entityType: string
    fieldName: string
    rejectReason: string
    userId: string
}

export function useGenericRejection() {
    const appStore = useAppStore()

    const { mutate: rejectFieldMutation, loading: rejecting } = useMutation(REJECT_FIELD)

    const rejectField = async (input: RejectFieldInput) => {
        try {
            const result = await rejectFieldMutation({
                input
            })

            appStore.showSnack('Field rejected successfully')
            return result?.data?.rejectField
        } catch (error) {
            console.error('Error rejecting field:', error)
            appStore.showSnack('Error rejecting field')
            throw error
        }
    }

    return {
        rejectField,
        rejecting
    }
} 